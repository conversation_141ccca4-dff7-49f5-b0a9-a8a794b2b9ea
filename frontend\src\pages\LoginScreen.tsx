import { useState, useContext  } from 'react'
import {
    <PERSON><PERSON>ield,
    <PERSON><PERSON>,
    Card,
    CardContent,
    Typography,
    Box,
    Alert,
    Container,
    InputAdornment,
    Snackbar
} from '@mui/material';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import { authApi } from '../services/authApi';
import { projectManagementAppContext } from '../App';
import { projectManagementAppContextType, Credentials } from '../types';
import { useAppNavigation } from '../hooks/useAppNavigation';

export const LoginScreen: React.FC = () => {
    const [email, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const { setIsAuthenticated, setUser } = useContext(projectManagementAppContext) as projectManagementAppContextType;
    const navigation = useAppNavigation();
    
    const handleCloseSnackbar = () => {
        setError('');
    };

    const handleLogin = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');

        const credentials: Credentials = {
            email,
            password
        };

        try {
            const result = await authApi.login(credentials);

            if (result.success && result.token && result.user) {
                localStorage.setItem('token', result.token);
                const storedToken = localStorage.getItem('token');
                if (storedToken) {
                    setUser(result.user);
                    setIsAuthenticated(true);
                    navigation.navigateToHome();
                } else {
                    setError('Failed to set authentication token');
                }
            } else {
                setError(result.message || 'Invalid username or password');
            }
        } catch (err) {
            console.error('Login error:', err);
            setError(err instanceof Error ? err.message : 'An error occurred. Please try again.');
        }
    };

    return (
        <Box
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            height="100vh"
            width="100%"
            overflow="hidden"
            bgcolor="#f8f9fa"  // Slightly lighter gray for better contrast
            sx={{
                backgroundImage: 'linear-gradient(to bottom right, #f8f9fa, #e9ecef)',
                backgroundAttachment: 'fixed'
            }}
        >
            <Container
                maxWidth="sm"
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                    overflow: 'auto',
                    py: 3
                }}
            >


            <Card
                elevation={0}
                sx={{
                    maxWidth: 500,
                    width: '100%',
                    boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
                    borderRadius: 4,
                    border: '1px solid rgba(0,0,0,0.04)',
                    backgroundColor: '#ffffff',
                    transition: 'box-shadow 0.3s ease-in-out',
                    overflow: 'hidden',
                    position: 'relative',
                    '&:hover': {
                        boxShadow: '0 4px 16px rgba(0,0,0,0.12)'
                    },
                    '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '5px',
                        background: 'linear-gradient(90deg, #2196f3, #64b5f6, #2196f3)',
                        zIndex: 1
                    },
                    '&::after': {
                        content: '""',
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        height: '5px',
                        background: 'linear-gradient(90deg, #2196f3 ,#09ea10)',
                        opacity: 0.6,
                        zIndex: 1
                    }
                }}
            >
                <CardContent sx={{ p: 3, pb: 3, pt: 5 }}>
                    <Box sx={{ textAlign: 'center', mb: 1 }}>
                        <Box
                            sx={{
                                mb: 1,
                                display: 'flex',
                                justifyContent: 'center',
                                mt: -3, // Reduced overlap for better positioning
                                position: 'relative'
                            }}
                        >
                            <Box
                                sx={{
                                    width: 100,
                                    height: 100,
                                    borderRadius: '50%',
                                    backgroundColor: 'white',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                    border: '1px solid rgba(0,0,0,0.04)',
                                    p: 1.5
                                }}
                            >
                                <img
                                    src="/logo-final.png"
                                    alt="NJSEI Logo"
                                    style={{
                                        maxWidth: '100%',
                                        maxHeight: '100%',
                                        objectFit: 'contain'
                                    }}
                                />
                            </Box>
                        </Box>
                        <Typography
                            variant="h6"
                            component="h1"
                            sx={{
                                fontWeight: 'bold',
                                color: '#1976d2',
                                lineHeight: 1.2,
                                mt: 1.5,
                                fontSize: '2rem'
                            }}
                        >
                            NJSEI ISO 9000 Forms
                        </Typography>
                        <Typography
                            variant="subtitle1"
                            sx={{
                                color: '#555',
                                mb: 0.5,
                                fontSize: '1.1rem'
                            }}
                        >
                            Project Management Application
                        </Typography>
                        <Typography
                            variant="body2"
                            sx={{
                                display: 'block',
                                color: '#666',
                                fontSize: '1.1rem',
                                mb: 1
                            }}
                        >
                            Version 1.5.1
                        </Typography>
                    </Box>

                    <Box
                        sx={{
                            pt: 1,
                            pb: 1,
                            borderTop: '1px solid rgba(0,0,0,0.1)',
                            mb: 1.5
                        }}
                    >
                        <Typography
                            variant="h6"
                            component="h2"
                            align="center"
                            sx={{
                                fontWeight: 600,
                                color: '#1976d2',
                                fontSize: '1.25rem'
                            }}
                        >
                            Login to your account
                        </Typography>
                    </Box>
                    <form onSubmit={handleLogin}>
                        <TextField
                            fullWidth
                            label="Email"
                            variant="outlined"
                            margin="dense"
                            value={email}
                            onChange={(e) => setUsername(e.target.value)}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <EmailIcon sx={{ color: '#1976d2' }} />
                                    </InputAdornment>
                                ),
                            }}
                            sx={{
                                mb: 2.5,
                                '& .MuiOutlinedInput-root': {
                                    borderRadius: 1,
                                    backgroundColor: '#fff',
                                    transition: 'all 0.2s',
                                    height: '48px',
                                    '&:hover': {
                                        borderColor: '#1976d2'
                                    },
                                    '&.Mui-focused': {
                                        borderColor: '#1976d2',
                                        boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.2)'
                                    }
                                },
                                '& .MuiInputLabel-root': {
                                    transform: 'translate(40px, 14px) scale(1)',
                                    color: '#666'
                                },
                                '& .MuiInputLabel-shrink': {
                                    transform: 'translate(14px, -6px) scale(0.75)',
                                    color: '#1976d2'
                                }
                            }}
                        />
                        <TextField
                            fullWidth
                            label="Password"
                            type="password"
                            variant="outlined"
                            margin="dense"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <LockIcon sx={{ color: '#1976d2' }} />
                                    </InputAdornment>
                                ),
                            }}
                            sx={{
                                mb: 2.5,
                                '& .MuiOutlinedInput-root': {
                                    borderRadius: 1,
                                    backgroundColor: '#fff',
                                    transition: 'all 0.2s',
                                    height: '48px',
                                    '&:hover': {
                                        borderColor: '#1976d2'
                                    },
                                    '&.Mui-focused': {
                                        borderColor: '#1976d2',
                                        boxShadow: '0 0 0 1px rgba(25, 118, 210, 0.2)'
                                    }
                                },
                                '& .MuiInputLabel-root': {
                                    transform: 'translate(40px, 14px) scale(1)',
                                    color: '#666'
                                },
                                '& .MuiInputLabel-shrink': {
                                    transform: 'translate(14px, -6px) scale(0.75)',
                                    color: '#1976d2'
                                }
                            }}
                        />
                        <Button
                            type="submit"
                            fullWidth
                            variant="contained"
                            color="primary"
                            size="large"
                            sx={{
                                mt: 1,
                                mb: 2,
                                py: 1.2,
                                borderRadius: 1,
                                textTransform: 'none',
                                fontSize: '1.2rem',
                                fontWeight: 600,
                                backgroundColor: '#1976d2',
                                boxShadow: 'none',
                                transition: 'all 0.2s',
                                '&:hover': {
                                    backgroundColor: '#1565c0',
                                    boxShadow: '0 2px 6px rgba(25, 118, 210, 0.3)'
                                },
                                '&:active': {
                                    backgroundColor: '#0d47a1',
                                    boxShadow: 'none'
                                }
                            }}
                        >
                            Log In
                        </Button>
                    </form>
                    <Typography variant="body2" align="center" sx={{ mt: 2, color: '#666' }}>
                        <a
                            href="#"
                            style={{
                                color: '#1976d2',
                                textDecoration: 'none',
                                fontWeight: 400,
                                fontSize: '0.9rem'
                            }}
                        >
                            Forgot password?
                        </a>
                    </Typography>
                </CardContent>
            </Card>
            {error && (
                <Snackbar
                    open={!!error}
                    autoHideDuration={3000}
                    onClose={handleCloseSnackbar}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                >
                    <Alert
                        severity="error"
                        variant="filled"
                        onClose={handleCloseSnackbar}
                        sx={{
                            maxWidth: 300,
                            boxShadow: 2,
                            borderRadius: 1,
                            border: '1px solid rgb(227, 27, 27,0.7)',
                            '& .MuiAlert-icon': {
                                color: '#fff',
                            }
                        }}
                    >
                        {error}
                    </Alert>
                </Snackbar>
            )}
            </Container>
        </Box>
    );
};

export default LoginScreen;
