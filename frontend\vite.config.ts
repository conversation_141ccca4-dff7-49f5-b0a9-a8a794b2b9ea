import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from "path"
import tailwindcss from "@tailwindcss/vite"

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },

  
  server: {
    host: '0.0.0.0', // Allow access from all network interfaces
    port: 5176, // Existing port from previous configuration
    strictPort: true,
    // Enable network access
    open: true,
    cors: true
  }
})
